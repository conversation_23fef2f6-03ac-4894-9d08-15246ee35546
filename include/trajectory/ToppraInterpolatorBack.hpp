#ifndef TOPPRA_INTERPOLATOR_H
#define TOPPRA_INTERPOLATOR_H

#include <vector>
#include <memory>
#include <cmath>
#include <iostream>

#include "TrajInterpolatorBase.hpp"

#include <toppra/algorithm/toppra.hpp>
#include <toppra/constraint/linear_joint_acceleration.hpp>
#include <toppra/constraint/linear_joint_velocity.hpp>
#include <toppra/parametrizer/spline.hpp>
#include <toppra/geometric_path/piecewise_poly_path.hpp>
#include <toppra/parametrizer/const_accel.hpp>
#include <toppra/solver/seidel.hpp>
#include <toppra/toppra.hpp>


#include <Eigen/Dense>

// TOPPRA trajectory interpolator implementation
template <int DOF>
class ToppraInterpolator : public TrajectoryInterpolator<DOF> {
public:
    using VectorDOF = typename TrajectoryInterpolator<DOF>::VectorDOF;
    using MatrixDOF = typename TrajectoryInterpolator<DOF>::MatrixDOF;

    ToppraInterpolator(double dt = 0.001, const std::string& urdf_path = "")
        : TrajectoryInterpolator<DOF>(dt, urdf_path) {}

    // Offline trajectory planning (multiple waypoints)
    bool computeOffline(const std::vector<MotionState<DOF>>& waypoints) override {
        this->clearError();

        if (waypoints.empty()) {
            this->setError("Empty waypoints provided");
            return false;
        }

        try {
            // Extract positions from MotionState waypoints
            toppra::Vectors positions;
            for (const auto& wp : waypoints) {
                positions.push_back(wp.position);
            }

            // Create time vector
            toppra::Vector times(positions.size());
            times.setLinSpaced(0, 1);

            // Set boundary conditions
            std::array<toppra::BoundaryCond, 2> boundary_cond;
            toppra::BoundaryCond bc{"clamped"};
            boundary_cond[0] = bc;
            boundary_cond[1] = bc;

            // Create path
            auto path_obj = toppra::PiecewisePolyPath::CubicSpline(positions, times, boundary_cond);
            path_ = std::make_shared<toppra::PiecewisePolyPath>(path_obj);

            // Build constraints using the new constraint structure
            toppra::LinearConstraintPtrs constraints;
            constraints.push_back(std::make_shared<toppra::constraint::LinearJointVelocity>(
                -this->constraints_.max_velocity, this->constraints_.max_velocity));
            constraints.push_back(std::make_shared<toppra::constraint::LinearJointAcceleration>(
                -this->constraints_.max_acceleration, this->constraints_.max_acceleration));

            // Build TOPPRA algorithm
            toppra::algorithm::TOPPRA algo(constraints, path_);
            algo.solver(std::make_shared<toppra::solver::Seidel>());
            algo.computePathParametrization(0, 0);
            data_ = algo.getParameterizationData();
            trajectory_ = std::make_shared<toppra::parametrizer::ConstAccel>(path_, data_.gridpoints, data_.parametrization);

            // Sample trajectory and store in buffer
            return sampleTrajectoryToBuffer();

        } catch (const std::exception& e) {
            this->setError("TOPPRA computation failed: " + std::string(e.what()));
            return false;
        }
    }

    // Online trajectory planning (TOPPRA is primarily an offline algorithm)
    bool computeOnline(const MotionState<DOF>& current_state,
                       const MotionState<DOF>& target_state) override {
        this->setError("TOPPRA is an offline algorithm. Use computeOffline() instead.");
        return false;
    }

    // Get interpolator type name
    std::string getTypeName() const override {
        return "ToppraInterpolator";
    }

private:

    std::shared_ptr<toppra::GeometricPath> path_;
    toppra::ParametrizationData data_;
    std::shared_ptr<toppra::parametrizer::ConstAccel> trajectory_;


    // Sample the computed trajectory and store in trajectory buffer
    bool sampleTrajectoryToBuffer() {

        if (!trajectory_) {
            this->setError("No trajectory computed");
            return false;
        }

        try {
            auto path_interval = trajectory_->pathInterval();
            double duration = path_interval[1] - path_interval[0];

            // Sample at the interpolator's time step
            int num_samples = static_cast<int>(std::ceil(duration / this->dt_)) + 1;

            std::vector<TrajectoryState<DOF>> states;
            std::vector<double> timestamps;

            states.reserve(num_samples);
            timestamps.reserve(num_samples);

            Eigen::VectorXd u(1);
            for (int i = 0; i < num_samples; ++i) {
                double t = std::min(i * this->dt_, duration);
                u(0) = t;

                TrajectoryState<DOF> state;
                state.position = trajectory_->eval(u, 0)[0];
                state.velocity = trajectory_->eval(u, 1)[0];
                state.acceleration = trajectory_->eval(u, 2)[0];
                state.timestamp = t;
                state.valid = true;

                states.push_back(state);
                timestamps.push_back(t);
            }

            // Store in trajectory buffer
            this->trajectory_buffer_.setTrajectory(states, timestamps);
            return true;

        } catch (const std::exception& e) {
            this->setError("Failed to sample trajectory: " + std::string(e.what()));
            return false;
        }
    }

};

#endif // TOPPRA_INTERPOLATOR_H