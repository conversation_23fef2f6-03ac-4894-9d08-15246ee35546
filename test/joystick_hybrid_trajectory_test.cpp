#include <zmq.h>
#include <iostream>
#include <cstring>
#include <chrono>
#include <thread>
#include <signal.h>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <atomic>
#include <cmath>
#include <array>
#include <algorithm>
#include "industRob.h"
#include "trajectory/HybridTrajectoryPlanner.hpp"

// 原始二进制数据结构，与Python端发送的数据格式对应
struct JoystickRawData {
    double x, y, z, rx, ry, rz;  // 位姿数据
    int start, suck, reset, record;  // 按钮状态
} __attribute__((packed));

// 手柄数据结构
struct JoystickData {
    double pose[6];  // x, y, z, rx, ry, rz
    int start;       // start按钮状态 (0/1)
    int suck;        // suck按钮状态 (0/1)
    int reset;       // reset按钮状态 (0/1)
    int record;      // record状态 (0=不记录, 1=普通, 2=记录中)
    bool valid;      // 数据有效性标志
    
    JoystickData() : start(0), suck(0), reset(0), record(0), valid(false) {
        for (int i = 0; i < 6; i++) pose[i] = 0.0;
    }
};

// 全局标志用于优雅退出
volatile bool running = true;

void signalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", exiting..." << std::endl;
    running = false;
}

// 混合轨迹控制器类
class HybridTrajectoryController {
private:
    static constexpr int DOF = 6;
    std::unique_ptr<HybridTrajectoryPlanner<DOF>> planner_;
    std::atomic<bool> offline_mode_{false};
    std::atomic<bool> running_{false};
    std::mutex state_mutex_;
    MotionState<DOF> last_online_target_;
    bool has_last_target_{false};

    // 在线轨迹更新控制
    std::chrono::steady_clock::time_point last_online_update_;
    static constexpr double MIN_UPDATE_INTERVAL = 0.1;  // 最小更新间隔100ms
    double position_threshold_ = 0.01;  // 位置变化阈值

    // 离线模式切换控制
    std::atomic<bool> just_switched_to_offline_{false};
    std::chrono::steady_clock::time_point offline_switch_time_;

    // 阻止手柄位置更新的标志（在离线轨迹执行期间）
    std::atomic<bool> block_joystick_updates_{false};

public:
    HybridTrajectoryController() {
        planner_ = std::make_unique<HybridTrajectoryPlanner<DOF>>(0.016);
        
        // 设置约束
        MotionConstraints<DOF> constraints;
        constraints.max_velocity << 2,1,0.5,1,1,1;  // 2 m/s, 1 m/s, 0.5 m/s, 1 rad/s
        constraints.max_acceleration.setConstant(8.0);  // 2.0 m/s²
        constraints.max_jerk.setConstant(20.0);         // 10.0 m/s³
        planner_->setConstraints(constraints);
        
        // 设置完成回调
        planner_->setCompletionCallback([this](size_t segment_id) {
            std::cout << "[HybridController] Offline trajectory segment " << segment_id << " completed" << std::endl;

            // 在切换回在线模式之前，确保last_online_target_是正确的结束位置
            // 这个值应该在轨迹执行过程中已经被更新了
            std::lock_guard<std::mutex> lock(state_mutex_);
            std::cout << "[HybridController] Final offline position: [" << last_online_target_.position.transpose() << "]" << std::endl;

            // 离线轨迹完成，切换回在线模式
            offline_mode_.store(false);
            just_switched_to_offline_.store(false);  // 重置切换标志
            block_joystick_updates_.store(false);    // 恢复手柄位置更新
            std::cout << "[HybridController] Switched back to ONLINE mode, allowing joystick updates" << std::endl;
        });
    }

    bool start() {
        if (!planner_->start()) {
            std::cerr << "[HybridController] Failed to start planner" << std::endl;
            return false;
        }
        running_.store(true);
        std::cout << "[HybridController] Started successfully" << std::endl;
        return true;
    }

    void stop() {
        running_.store(false);
        planner_->stop();
        std::cout << "[HybridController] Stopped" << std::endl;
    }

    bool addOnlineTarget(const std::vector<double>& pose) {
        if (offline_mode_.load()) {
            // 离线模式时拒绝在线轨迹
            return false;
        }

        if (block_joystick_updates_.load()) {
            // 阻止手柄更新期间拒绝位置更新
            static int blocked_count = 0;
            if (++blocked_count % 100 == 0) {
                std::cout << "[ONLINE] Joystick updates blocked during trajectory transition (count: "
                          << blocked_count << ")" << std::endl;
            }
            return false;
        }

        std::lock_guard<std::mutex> lock(state_mutex_);

        // 转换为MotionState
        MotionState<DOF> target;
        for (int i = 0; i < DOF; ++i) {
            target.position[i] = pose[i];
            target.velocity[i] = 0.0;
            target.acceleration[i] = 0.0;
        }

        // 检查位置跳跃，防止不连续
        if (has_last_target_) {
            double distance = 0.0;
            for (int i = 0; i < 3; ++i) {  // 只检查XYZ位置
                double diff = target.position[i] - last_online_target_.position[i];
                distance += diff * diff;
            }
            distance = std::sqrt(distance);

            // 如果位置跳跃过大（超过10cm），可能是手柄重置，需要特殊处理
            if (distance > 0.1) {
                static int jump_count = 0;
                std::cout << "[ONLINE] Large position jump detected: " << distance
                          << "m from [" << last_online_target_.position.transpose()
                          << "] to [" << target.position.transpose() << "] (jump #" << ++jump_count << ")" << std::endl;

                // 对于大跳跃，我们拒绝直接应用，可能是按钮触发的轨迹规划
                std::cout << "[ONLINE] Rejecting large jump - likely button-triggered trajectory planning" << std::endl;
                return false;
            }
        }

        // 更新在线目标
        last_online_target_ = target;
        has_last_target_ = true;

        static int update_count = 0;
        if (++update_count % 50 == 0) {
            std::cout << "[ONLINE] Direct target update: ["
                      << pose[0] << ", " << pose[1] << ", " << pose[2] << "] "
                      << "(count: " << update_count << ")" << std::endl;
        }

        return true;  // 成功更新在线目标
    }

    bool startOfflineTrajectory(const std::vector<double>& target_pose = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, double safe_height = 0.1) {
        if (!has_last_target_) {
            std::cerr << "[HybridController] No last target available for offline planning" << std::endl;
            return false;
        }

        std::lock_guard<std::mutex> lock(state_mutex_);

        std::cout << "[HybridController] Starting offline trajectory planning..." << std::endl;

        // 获取当前位置 - 确保使用实际的当前位置作为起点
        MotionState<DOF> current_state = last_online_target_;
        double current_z = current_state.position[2];
        double target_z = target_pose[2];

        std::cout << "[HybridController] Current position: [" << current_state.position.transpose()
                  << "], Target Z: " << target_z << ", Safe height: " << safe_height << std::endl;

        std::vector<MotionState<DOF>> waypoints;

        // 确保起点是当前实际位置，避免跳跃
        MotionState<DOF> start_state = current_state;
        start_state.velocity.setZero();
        start_state.acceleration.setZero();
        waypoints.push_back(start_state);  // 起点

        // 检查是否需要过渡点
        if (current_z < safe_height || current_z < target_z + 0.02) {  // 需要抬高（目标高度+2cm安全余量）
            // 过渡点1：在当前XY位置抬高到安全高度
            MotionState<DOF> lift_state = current_state;
            lift_state.position[2] = std::max(safe_height, target_z + 0.02);
            lift_state.velocity.setZero();
            lift_state.acceleration.setZero();
            waypoints.push_back(lift_state);

            std::cout << "[HybridController] Added lift waypoint: Z=" << lift_state.position[2] << std::endl;

            // 检查XY位置是否需要移动
            double dx = std::abs(current_state.position[0] - target_pose[0]);
            double dy = std::abs(current_state.position[1] - target_pose[1]);
            if (dx > 0.001 || dy > 0.001) {  // 如果XY位置差异大于1mm，添加移动过渡点
                // 过渡点2：移动到目标XY位置，保持安全高度
                MotionState<DOF> move_state = lift_state;
                move_state.position[0] = target_pose[0];  // X
                move_state.position[1] = target_pose[1];  // Y
                move_state.position[3] = target_pose[3];  // RX
                move_state.position[4] = target_pose[4];  // RY
                move_state.position[5] = target_pose[5];  // RZ
                waypoints.push_back(move_state);

                std::cout << "[HybridController] Added move waypoint: XY=(" << move_state.position[0]
                          << ", " << move_state.position[1] << "), dXY=(" << dx << ", " << dy << ")" << std::endl;
            } else {
                std::cout << "[HybridController] Skipping move waypoint: already at target XY position" << std::endl;
            }
        }

        // 最终目标点
        MotionState<DOF> end_state;
        for (int i = 0; i < DOF; ++i) {
            end_state.position[i] = target_pose[i];
        }
        end_state.velocity.setZero();
        end_state.acceleration.setZero();
        waypoints.push_back(end_state);

        std::cout << "[HybridController] Planning offline trajectory with " << waypoints.size()
                  << " waypoints to [" << end_state.position.transpose() << "]" << std::endl;

        // 先添加轨迹段，成功后再切换模式
        bool success = planner_->addTrajectorySegment(waypoints, true);  // immediate execution

        if (success) {
            // 轨迹添加成功，现在切换到离线模式
            offline_mode_.store(true);
            just_switched_to_offline_.store(true);
            block_joystick_updates_.store(true);  // 阻止手柄位置更新
            offline_switch_time_ = std::chrono::steady_clock::now();
            std::cout << "[HybridController] Switched to OFFLINE mode, blocking joystick updates" << std::endl;

            // 注意：不要立即更新last_online_target_为终点，保持当前位置用于缓冲
            // last_online_target_ = end_state;  // 这行导致了跳跃问题
        } else {
            std::cerr << "[HybridController] Failed to add trajectory segment" << std::endl;
        }

        return success;
    }

    TrajectoryState<DOF> getCurrentTarget(double current_time) {
        if (!running_.load()) {
            return TrajectoryState<DOF>();
        }

        if (offline_mode_.load()) {
            // 检查是否刚切换到离线模式
            if (just_switched_to_offline_.load()) {
                auto now = std::chrono::steady_clock::now();
                auto time_since_switch = std::chrono::duration<double>(now - offline_switch_time_).count();

                // 在切换后的前几个控制周期内，强制保持当前位置
                if (time_since_switch < 0.1) {  // 100ms缓冲期
                    std::lock_guard<std::mutex> lock(state_mutex_);
                    if (has_last_target_) {
                        TrajectoryState<DOF> hold_state;
                        hold_state.position = last_online_target_.position;
                        hold_state.velocity.setZero();
                        hold_state.acceleration.setZero();
                        hold_state.timestamp = current_time;
                        hold_state.valid = true;

                        static int hold_count = 0;
                        if (++hold_count % 5 == 0) {
                            std::cout << "[HybridController] Holding position during offline transition: ["
                                      << hold_state.position.transpose() << "] (count: " << hold_count
                                      << ", time: " << time_since_switch << "s)" << std::endl;
                        }
                        return hold_state;
                    }
                } else {
                    // 缓冲期结束，开始正常的离线轨迹
                    just_switched_to_offline_.store(false);
                    std::cout << "[HybridController] Transition period ended, starting offline trajectory execution" << std::endl;
                }
            }

            // 离线模式：从轨迹规划器获取
            TrajectoryState<DOF> offline_state = planner_->getCurrentTarget(current_time);
            if (offline_state.valid) {
                // 在离线模式执行过程中，持续更新last_online_target_为当前执行位置
                std::lock_guard<std::mutex> lock(state_mutex_);
                MotionState<DOF> current_motion_state;
                current_motion_state.position = offline_state.position;
                current_motion_state.velocity = offline_state.velocity;
                current_motion_state.acceleration = offline_state.acceleration;
                last_online_target_ = current_motion_state;

                return offline_state;
            } else {
                // 如果离线轨迹无效，返回最后的有效位置避免跳到(0,0,0)
                std::lock_guard<std::mutex> lock(state_mutex_);
                if (has_last_target_) {
                    TrajectoryState<DOF> fallback_state;
                    fallback_state.position = last_online_target_.position;
                    fallback_state.velocity.setZero();
                    fallback_state.acceleration.setZero();
                    fallback_state.timestamp = current_time;
                    fallback_state.valid = true;
                    static int fallback_count = 0;
                    if (++fallback_count % 50 == 0) {
                        std::cout << "[HybridController] Using fallback position during offline transition (count: "
                                  << fallback_count << ")" << std::endl;
                    }
                    return fallback_state;
                } else {
                    return TrajectoryState<DOF>();  // 无效状态
                }
            }
        } else {
            // 在线模式：直接返回当前目标
            std::lock_guard<std::mutex> lock(state_mutex_);
            if (has_last_target_) {
                TrajectoryState<DOF> state;
                state.position = last_online_target_.position;
                state.velocity = last_online_target_.velocity;
                state.acceleration = last_online_target_.acceleration;
                state.timestamp = current_time;
                state.valid = true;
                return state;
            } else {
                return TrajectoryState<DOF>();  // 无效状态
            }
        }
    }

    bool isOfflineMode() const {
        return offline_mode_.load();
    }

    bool isRunning() const {
        return running_.load();
    }
};

// 轨迹执行器类
class TrajectoryExecutor {
private:
    HybridTrajectoryController* controller_;
    industRob* robot_;
    std::thread executor_thread_;
    std::atomic<bool> running_{false};
    static constexpr double CONTROL_PERIOD = 0.016;  // 16ms = 62.5Hz

public:
    TrajectoryExecutor(HybridTrajectoryController* controller, industRob* robot)
        : controller_(controller), robot_(robot) {}

    ~TrajectoryExecutor() {
        stop();
    }

    void start() {
        running_.store(true);
        executor_thread_ = std::thread(&TrajectoryExecutor::executorLoop, this);
        std::cout << "[TrajectoryExecutor] Started executor thread" << std::endl;
    }

    void stop() {
        if (running_.load()) {
            running_.store(false);
            if (executor_thread_.joinable()) {
                executor_thread_.join();
            }
            std::cout << "[TrajectoryExecutor] Stopped executor thread" << std::endl;
        }
    }

private:
    void executorLoop() {
        std::cout << "[TrajectoryExecutor] Executor thread started" << std::endl;

        const std::chrono::microseconds cycle_time(static_cast<int>(CONTROL_PERIOD * 1000000));
        auto next_cycle_time = std::chrono::steady_clock::now();
        auto start_time = std::chrono::steady_clock::now();

        int execution_count = 0;

        while (running_.load() && controller_->isRunning()) {
            auto cycle_start = std::chrono::steady_clock::now();
            next_cycle_time += cycle_time;

            // 计算从开始的时间
            double elapsed_time = std::chrono::duration<double>(cycle_start - start_time).count();

            // 获取当前目标状态
            TrajectoryState<6> target = controller_->getCurrentTarget(elapsed_time);

            if (target.valid) {
                try {
                    // 转换为std::vector<double>
                    std::vector<double> pose(6);
                    for (int i = 0; i < 6; ++i) {
                        pose[i] = target.position[i];
                    }

                    // 执行伺服控制
                    robot_->servoL(pose, elapsed_time, 0.1, 0.3);

                    // 定期打印状态
                    if (++execution_count % 50 == 0) {
                        std::cout << "[TrajectoryExecutor] Executing: ["
                                  << pose[0] << ", " << pose[1] << ", " << pose[2] << "] "
                                  << (controller_->isOfflineMode() ? "OFFLINE" : "ONLINE")
                                  << " mode (count: " << execution_count << ", time: " << elapsed_time << "s)" << std::endl;
                    }

                } catch (const std::exception& e) {
                    std::cerr << "[TrajectoryExecutor] Error executing servoL: " << e.what() << std::endl;
                }
            } else {
                // 没有有效轨迹时的处理
                static int invalid_count = 0;
                static bool waiting_logged = false;

                if (!waiting_logged) {
                    std::cout << "[TrajectoryExecutor] Waiting for valid trajectory..." << std::endl;
                    waiting_logged = true;
                }

                if (++invalid_count % 500 == 0) {
                    std::cout << "[TrajectoryExecutor] Still waiting for trajectory (count: " << invalid_count
                              << ", time: " << elapsed_time << "s, mode: "
                              << (controller_->isOfflineMode() ? "OFFLINE" : "ONLINE") << ")" << std::endl;
                }

                // 没有轨迹时不发送servoL命令，避免发送无效位置
            }

            // 精确等待到下一个周期
            auto now = std::chrono::steady_clock::now();
            if (now < next_cycle_time) {
                std::this_thread::sleep_until(next_cycle_time);
            } else {
                // 处理周期超时
                auto cycles_behind = std::chrono::duration_cast<std::chrono::microseconds>(now - next_cycle_time).count() / cycle_time.count() + 1;
                next_cycle_time += cycle_time * cycles_behind;
                std::cerr << "[TrajectoryExecutor] Warning: Cycle time overrun by " << cycles_behind << " cycles" << std::endl;
            }
        }

        std::cout << "[TrajectoryExecutor] Executor thread finished" << std::endl;
    }
};

// ZMQ接收器类 (复用tele_zmq_test.cpp的逻辑)
class SimpleZmqReceiver {
private:
    void* context_;
    void* socket_;
    bool connected_;
    bool running_;
    std::thread receiver_thread_;
    std::function<void(const JoystickData&)> data_callback_;

public:
    SimpleZmqReceiver() : context_(nullptr), socket_(nullptr), connected_(false), running_(false) {}

    ~SimpleZmqReceiver() {
        stop();
        disconnect();
    }

    // 注册回调函数
    void registerCallback(const std::function<void(const JoystickData&)>& callback) {
        data_callback_ = callback;
        std::cout << "[ZmqReceiver] Callback registered" << std::endl;
    }

    bool connect() {
        try {
            context_ = zmq_ctx_new();
            if (!context_) {
                std::cerr << "[ZmqReceiver] Failed to create ZMQ context" << std::endl;
                return false;
            }

            socket_ = zmq_socket(context_, ZMQ_SUB);
            if (!socket_) {
                std::cerr << "[ZmqReceiver] Failed to create ZMQ socket" << std::endl;
                zmq_ctx_destroy(context_);
                context_ = nullptr;
                return false;
            }

            // 连接到发布者
            int rc = zmq_connect(socket_, "ipc:///tmp/joystick_control");
            if (rc != 0) {
                std::cerr << "[ZmqReceiver] Failed to connect: " << zmq_strerror(errno) << std::endl;
                zmq_close(socket_);
                zmq_ctx_destroy(context_);
                socket_ = nullptr;
                context_ = nullptr;
                return false;
            }

            // 订阅主题
            rc = zmq_setsockopt(socket_, ZMQ_SUBSCRIBE, "joystick_data", 12);
            if (rc != 0) {
                std::cerr << "[ZmqReceiver] Failed to subscribe: " << zmq_strerror(errno) << std::endl;
                zmq_close(socket_);
                zmq_ctx_destroy(context_);
                socket_ = nullptr;
                context_ = nullptr;
                return false;
            }

            // 设置非阻塞模式
            int timeout = 0;
            zmq_setsockopt(socket_, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));

            connected_ = true;
            std::cout << "[ZmqReceiver] Connected to ipc:///tmp/joystick_control" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ZmqReceiver] Exception in connect(): " << e.what() << std::endl;
            return false;
        }
    }

    void disconnect() {
        connected_ = false;
        if (socket_) {
            zmq_close(socket_);
            socket_ = nullptr;
        }
        if (context_) {
            zmq_ctx_destroy(context_);
            context_ = nullptr;
        }
    }

    // 启动接收线程
    void start() {
        if (!connected_ || !data_callback_) {
            std::cerr << "[ZmqReceiver] Cannot start: not connected or no callback registered" << std::endl;
            return;
        }

        running_ = true;
        receiver_thread_ = std::thread(&SimpleZmqReceiver::receiverLoop, this);
        std::cout << "[ZmqReceiver] Started receiver thread" << std::endl;
    }

    // 停止接收线程
    void stop() {
        running_ = false;
        if (receiver_thread_.joinable()) {
            receiver_thread_.join();
            std::cout << "[ZmqReceiver] Stopped receiver thread" << std::endl;
        }
    }

private:
    // 接收线程主循环
    void receiverLoop() {
        std::cout << "[ZmqReceiver] Receiver thread started" << std::endl;

        while (running_ && connected_) {
            JoystickData data;
            if (receiveData(data) && data_callback_) {
                data_callback_(data);
            }

            // 短暂休眠避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::microseconds(1000));
        }

        std::cout << "[ZmqReceiver] Receiver thread finished" << std::endl;
    }

    // RAII包装器用于自动管理ZMQ消息
    struct ZmqMessage {
        zmq_msg_t msg;
        ZmqMessage() { zmq_msg_init(&msg); }
        ~ZmqMessage() { zmq_msg_close(&msg); }
        zmq_msg_t* get() { return &msg; }
    };

    // 接收ZMQ消息的辅助函数
    bool receiveZmqMessage(ZmqMessage& msg, const char* error_context) {
        int rc = zmq_msg_recv(msg.get(), socket_, ZMQ_DONTWAIT);
        if (rc == -1) {
            if (errno != EAGAIN) {
                std::cerr << "[ZmqReceiver] Failed to receive " << error_context
                         << ": " << zmq_strerror(errno) << std::endl;
            }
            return false;
        }
        return true;
    }

    // 接收数据的内部方法
    bool receiveData(JoystickData& data) {
        if (!connected_) return false;

        try {
            // 接收主题部分
            ZmqMessage topic_msg;
            if (!receiveZmqMessage(topic_msg, "topic")) {
                return false;
            }

            // 检查是否有数据部分
            int more;
            size_t more_size = sizeof(more);
            zmq_getsockopt(socket_, ZMQ_RCVMORE, &more, &more_size);
            if (!more) {
                std::cerr << "[ZmqReceiver] Expected multipart message" << std::endl;
                return false;
            }

            // 接收数据部分
            ZmqMessage data_msg;
            if (!receiveZmqMessage(data_msg, "data")) {
                return false;
            }

            // 验证数据大小
            size_t data_size = zmq_msg_size(data_msg.get());
            if (data_size != sizeof(JoystickRawData)) {
                std::cerr << "[ZmqReceiver] Data size mismatch. Expected: " << sizeof(JoystickRawData)
                         << ", Got: " << data_size << std::endl;
                return false;
            }

            // 解析并转换数据
            const JoystickRawData* raw_data = static_cast<const JoystickRawData*>(zmq_msg_data(data_msg.get()));

            // 直接赋值位姿数据
            data.pose[0] = raw_data->x;   data.pose[1] = raw_data->y;   data.pose[2] = raw_data->z;
            data.pose[5] = raw_data->rx;  data.pose[4] = raw_data->ry;  data.pose[3] = raw_data->rz;

            // 赋值按钮状态
            data.start = raw_data->start;
            data.suck = raw_data->suck;
            data.reset = raw_data->reset;
            data.record = raw_data->record;
            data.valid = true;

            return true;

        } catch (const std::exception& e) {
            std::cerr << "[ZmqReceiver] Exception in receiveData(): " << e.what() << std::endl;
            return false;
        }
    }
};

int main() {
    // // 注册信号处理器
    // signal(SIGINT, signalHandler);
    // signal(SIGTERM, signalHandler);

    std::cout << "=== 混合轨迹测试 - 手柄控制版本 ===" << std::endl;

    // 创建机器人控制实例
    industRob robot(CommType::ZMQ, "../config/cartesian_robot/urdf/cartesian_6axis.urdf", "tool0");

    // 连接机器人
    std::cout << "Connecting to robot..." << std::endl;
    robot.connect();

    if (!robot.isConnected()) {
        std::cerr << "Failed to connect to robot" << std::endl;
        return 1;
    }

    std::cout << "Robot connected successfully" << std::endl;

    // 创建混合轨迹控制器
    HybridTrajectoryController hybrid_controller;
    if (!hybrid_controller.start()) {
        std::cerr << "Failed to start hybrid trajectory controller" << std::endl;
        robot.disconnect();
        return 1;
    }

    // 使用合理的初始位置（在工作空间内）
    std::vector<double> initial_pose = {0.0, 0.0, 0.5, 0.0, 0.0, 0.0};  // 安全的初始位置
    std::cout << "Using safe initial pose: [0, 0, 0.5, 0, 0, 0]" << std::endl;

    // 不立即添加初始目标，等待手柄输入
    std::cout << "Waiting for first joystick input to initialize trajectory..." << std::endl;

    // 创建轨迹执行器
    TrajectoryExecutor trajectory_executor(&hybrid_controller, &robot);
    trajectory_executor.start();

    // 创建手柄数据接收器
    SimpleZmqReceiver receiver;

    // 按钮配置表 - 参数向量化
    struct ButtonConfig {
        std::string name;
        std::vector<double> target;
        double safe_height;
        std::string message;
    };

    // 按钮配置数组：索引0=START, 1=RECORD, 2=RESET
    std::array<ButtonConfig, 3> button_configs = {{
        {"START", {0.2, 0.1, 0.03, 0.0, 0.0, 0.0}, 0.1, "Starting offline trajectory to START_POINT (0.2,0.1,0.03,0,0,0)"},
        {"RECORD", {0.3, 0.01, 0.003, 0.0, 0.0, 0.0}, 0.1, "Starting offline trajectory to PLACE_POINT (0.3,0.01,0.003,0,0,0)"},
        {"RESET", {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, 0.05, "Starting offline trajectory to RESET_POINT (0,0,0,0,0,0)"}
    }};

    // 状态变量向量化
    int message_count = 0;
    auto last_print_time = std::chrono::steady_clock::now();
    std::array<int, 3> last_buttons = {0, 0, 0};  // last_start, last_record, last_reset
    int last_suck = 0;

    // 统一按钮处理函数
    auto handle_button_press = [&](int button_index, int current_value, int& last_value) -> bool {
        if (current_value == 1 && last_value == 0) {
            const auto& config = button_configs[button_index];
            std::cout << ">>> " << config.name << "=1: " << config.message << " with safe transitions..." << std::endl;
            try {
                if (hybrid_controller.startOfflineTrajectory(config.target, config.safe_height)) {
                    std::cout << ">>> " << config.name << " offline trajectory started successfully" << std::endl;
                } else {
                    std::cerr << ">>> Failed to start " << config.name << " offline trajectory" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cerr << ">>> Error starting " << config.name << " offline trajectory: " << e.what() << std::endl;
            }
            return true;
        }
        return false;
    };

    // 注册数据处理回调函数
    receiver.registerCallback([&](const JoystickData& data) {
        message_count++;
        auto current_time = std::chrono::steady_clock::now();

        auto time_since_last_print = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - last_print_time);

        // 检测记录状态变化（record=2表示正在记录）
        if (data.record == 2 && last_buttons[1] != 2) {
            std::cout << ">>> RECORDING started..." << std::endl;
        } else if (last_buttons[1] == 2 && data.record == 0) {
            std::cout << ">>> RECORDING stopped." << std::endl;
        }

        // 核心逻辑：根据按钮状态执行不同的动作（向量化处理）
        std::array<int, 3> current_buttons = {data.start, data.record, data.reset};
        bool button_pressed = false;

        // 按优先级处理按钮：START(0) > RECORD(1) > RESET(2)
        for (int i = 0; i < 3; ++i) {
            if (handle_button_press(i, current_buttons[i], last_buttons[i])) {
                button_pressed = true;
                break;  // 只处理第一个被按下的按钮
            }
        }

        // 只有当所有按钮都未按下且数据有效时，才处理在线轨迹
        // 注意：record按钮可能是2（正在记录），也需要阻止在线轨迹
        bool any_button_active = (current_buttons[0] == 1) || (current_buttons[1] >= 1) || (current_buttons[2] == 1);
        if (!button_pressed && !any_button_active && data.valid) {
            // 只有当start、reset、record都不活跃时，才能将位姿传入servol：添加在线轨迹点
            std::vector<double> pose(data.pose, data.pose + 6);

            if (hybrid_controller.addOnlineTarget(pose)) {
                // 成功添加在线目标
                static int online_count = 0;
                if (++online_count % 10 == 0) {
                    std::cout << "[ONLINE] Added target: ["
                              << pose[0] << ", " << pose[1] << ", " << pose[2] << "] "
                              << "(count: " << online_count << ")" << std::endl;
                }
            } else {
                // 在线目标被拒绝（可能在离线模式）
                static int reject_count = 0;
                if (++reject_count % 50 == 0) {
                    std::cout << "[ONLINE] Target rejected (offline mode active, count: "
                              << reject_count << ")" << std::endl;
                }
            }
        }

        // 调试：打印按钮状态变化（向量化）
        std::array<const char*, 3> button_names = {"START", "RECORD", "RESET"};
        bool state_changed = false;
        for (int i = 0; i < 3; ++i) {
            if (current_buttons[i] != last_buttons[i]) {
                if (!state_changed) {
                    std::cout << "[BUTTON] State change: ";
                    state_changed = true;
                }
                std::cout << button_names[i] << "(" << last_buttons[i] << "->" << current_buttons[i] << ") ";
            }
        }
        if (state_changed) {
            std::cout << std::endl;
        }

        // 更新上次状态（向量化）
        last_buttons = current_buttons;
        last_suck = data.suck;

        // 定期打印状态信息
        if (time_since_last_print.count() >= 2000) {  // 每2秒打印一次
            std::cout << "[STATUS] Messages: " << message_count
                      << ", Mode: " << (hybrid_controller.isOfflineMode() ? "OFFLINE" : "ONLINE")
                      << ", Pose: [" << data.pose[0] << ", " << data.pose[1] << ", " << data.pose[2] << "]"
                      << ", Buttons: S:" << data.start << " Su:" << data.suck
                      << " R:" << data.reset << " Rec:" << data.record << std::endl;
            last_print_time = current_time;
        }
    });

    // 连接到发布者
    if (!receiver.connect()) {
        std::cerr << "Failed to connect to joystick publisher" << std::endl;
        trajectory_executor.stop();
        hybrid_controller.stop();
        robot.disconnect();
        return 1;
    }

    // 启动接收器
    receiver.start();

    std::cout << "Waiting for joystick data..." << std::endl;
    std::cout << "Press Ctrl+C to exit" << std::endl;
    std::cout << "Usage:" << std::endl;
    std::cout << "  - Move joystick to send online trajectory points (only when no buttons are pressed)" << std::endl;
    std::cout << "  - Press START button to trigger offline trajectory to START_POINT (0.2,0.1,0.03,0,0,0)" << std::endl;
    std::cout << "  - Press RECORD button to trigger offline trajectory to PLACE_POINT (0.3,0.01,0.003,0,0,0)" << std::endl;
    std::cout << "  - Press RESET button to trigger offline trajectory to RESET_POINT (0,0,0,0,0,0)" << std::endl;
    std::cout << "  - During offline trajectory execution, online points will be rejected" << std::endl;
    std::cout << "  - Online trajectory only works when START, RESET, and RECORD buttons are all released" << std::endl;
    std::cout << std::string(80, '-') << std::endl;

    // 主循环等待退出信号
    while (running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    std::cout << "\nTotal messages received: " << message_count << std::endl;

    // 停止所有组件
    receiver.stop();
    trajectory_executor.stop();
    hybrid_controller.stop();
    receiver.disconnect();
    robot.disconnect();

    std::cout << "All components stopped and disconnected successfully" << std::endl;

    return 0;
}

