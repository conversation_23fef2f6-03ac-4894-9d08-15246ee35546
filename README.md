# Robot Infrastructure

A comprehensive C++ robot control and communication system with advanced trajectory planning capabilities.

## Features

- **Real-time Communication**: ZeroMQ-based communication system for robot control
- **Advanced Trajectory Planning**: Integration with <PERSON><PERSON>ig and TOPPRA libraries with multiple interpolation methods
- **Hybrid Trajectory Control**: Seamless switching between online and offline trajectory modes
- **Joystick Control**: Real-time joystick-based robot control with trajectory planning
- **Kinematics**: Forward and inverse kinematics calculations with Pinocchio integration
- **Industrial Robot Interface**: Complete API for robot control and monitoring
- **Multi-threaded Architecture**: Separate threads for communication and control
- **Flexible Path Interpolation**: Multiple interpolation methods to ensure trajectories pass through waypoints

## Project Structure

```
robot_infra/
├── CMakeLists.txt          # Root build configuration
├── README.md               # This file
├── include/                # Header files
│   ├── comm/              # Communication interfaces
│   ├── trajectory/        # Trajectory planning
│   └── industRob.h        # Main robot interface
├── src/                   # Source files
│   ├── comm/              # Communication implementation
│   ├── trajectory/        # Trajectory planning implementation
│   └── industRob.cpp      # Main robot implementation
├── test/                  # Test programs and examples
│   ├── comm_test.cpp      # Communication tests
│   ├── hw_sim_test.cpp    # Hardware simulation tests
│   ├── toppra_fifo_demo.cpp # TOPPRA demonstration
│   ├── joystick_hybrid_trajectory_test.cpp # Joystick control with hybrid trajectory
│   ├── tele_zmq_test.cpp  # Teleoperation ZMQ test
│   └── toppra/            # TOPPRA examples
├── docs/                  # Documentation
│   └── TOPPRA_INTERPOLATION_METHODS.md # TOPPRA interpolation guide
├── examples/              # Example programs
│   └── toppra_interpolation_methods_demo.cpp # TOPPRA interpolation demo
├── python/                # Python utilities
│   ├── JoystickController.py # Joystick control interface
│   └── trajInter.py       # Trajectory interpolation utilities
├── cmake/                 # CMake modules
└── 3rdparty/              # Third-party libraries
    ├── ruckig/            # Ruckig trajectory generation
    └── toppra/            # TOPPRA path parameterization
```

## Dependencies

### Required
- **Ubuntu 18.04+** or compatible Linux distribution
- **C++17** compiler (GCC 7+ or Clang 6+)
- **CMake 3.16+**
- **Eigen3** - Linear algebra library
- **ZeroMQ** - High-performance messaging library
- **Python3** development headers (for matplotlib integration)
- **Pinocchio** - Robotics kinematics and dynamics library

### Optional
- **Ruckig** - Real-time trajectory generation
- **TOPPRA** - Time-optimal path parameterization
- **Doxygen** - API documentation generation
- **Matplotlib** - Plotting capabilities
- **PyGame** - For joystick control interface

## Installation

### 1. Install System Dependencies

```bash
# Update package list
sudo apt update

# Install build tools and libraries
sudo apt install -y \
    build-essential \
    cmake \
    libeigen3-dev \
    libzmq3-dev \
    python3-dev \
    python3-numpy \
    python3-matplotlib \
    python3-pygame \
    pkg-config \
    git

# Install Pinocchio (robotics library)
sudo apt install -y robotpkg-pinocchio

# Optional: Install Doxygen for documentation
sudo apt install -y doxygen
```

### 2. Clone Repository

```bash
git clone <your-repository-url>
cd robot_infra
```

### 3. Build Third-party Libraries (Optional)

#### Build Ruckig
```bash
cd 3rdparty
git clone https://github.com/pantor/ruckig.git
cd ruckig
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
sudo make install
cd ../../..
```

#### Build TOPPRA
```bash
cd 3rdparty
git clone -b develop https://github.com/hungpham2511/toppra.git
cd toppra/cpp
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)
cd ../../../..
```

### 4. Build Project

```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

#### Build Options

```bash
# Enable debug mode (uses test_ prefixed FIFO/ZMQ endpoints)
cmake -DENABLE_DEBUG=ON ..

# Build with documentation
cmake -DBUILD_DOCUMENTATION=ON ..
make doc
```

## Usage

### Basic Robot Control

```cpp
#include "industRob.h"

int main() {
    industRob robot;
    
    // Initialize and connect
    robot.connect();
    robot.init();
    
    // Move to joint position
    std::vector<double> joints = {0.1, 0.2, 0.3, 0.0, 0.0, 0.0};
    robot.movAbsJ(joints, 0.5, 0.1, 0.01);
    
    // Wait for completion
    robot.waitRobFree();
    
    return 0;
}
```

### Real-time Servo Control

```cpp
// Enable servo mode
robot.servoMode(1, 10, 1.0, 0.1);  // mode, period(ms), smooth, response

// Send real-time commands
std::vector<double> target_joints = {0.1, 0.2, 0.3, 0.0, 0.0, 0.0};
robot.servoj(target_joints, 0.01, 0.1, 0.5);  // joints, dt, lookahead, gain
```

### Hybrid Trajectory Control

```cpp
#include "trajectory/HybridTrajectoryPlanner.hpp"

// Create hybrid trajectory controller
HybridTrajectoryPlanner<6> planner(0.016);  // 16ms control cycle

// Set motion constraints
MotionConstraints<6> constraints;
constraints.max_velocity << 1.0, 1.0, 0.5, 1.0, 1.0, 1.0;
constraints.max_acceleration.setConstant(2.0);
constraints.max_jerk.setConstant(10.0);
planner.setConstraints(constraints);

// Online trajectory (real-time)
MotionState<6> target;
target.position << 0.1, 0.2, 0.3, 0.0, 0.0, 0.0;
planner.addOnlineTarget(target);

// Offline trajectory (pre-planned)
std::vector<MotionState<6>> waypoints = {wp1, wp2, wp3};
planner.addTrajectorySegment(waypoints, true);  // immediate execution
```

### TOPPRA with Multiple Interpolation Methods

```cpp
#include "trajectory/ToppraInterpolatorBack.hpp"

ToppraInterpolator<6> interpolator(0.01);

// Method 1: Cubic Hermite Spline (strict waypoint passage)
interpolator.setInterpolationMethod(PathInterpolationMethod::CUBIC_HERMITE_SPLINE);

// Method 2: Linear segments (strict waypoint passage, not smooth)
interpolator.setInterpolationMethod(PathInterpolationMethod::LINEAR_SEGMENTS);

// Method 3: Cubic spline (smooth but may overshoot waypoints)
interpolator.setInterpolationMethod(PathInterpolationMethod::CUBIC_SPLINE);
interpolator.setBoundaryCondition(BoundaryConditionType::CLAMPED);

// Compute trajectory
std::vector<MotionState<6>> waypoints = {wp1, wp2, wp3};
bool success = interpolator.computeOffline(waypoints);
```

### Joystick Control

```python
# Python joystick controller
from JoystickController import JoystickController

controller = JoystickController()
controller.start_publishing()  # Publishes to ZMQ

# Button mappings:
# START: Move to start position (0.21, 0.12, 0.03)
# RECORD: Move to place position (0.285, 0.01, 0.003)
# RESET: Move to reset position (0, 0, 0)
# Joystick: Real-time position control
```

### Running Tests

```bash
cd build

# Test communication system
./comm_test

# Test hardware simulation
./hw_sim_test

# Test TOPPRA integration
./toppra_fifo_demo

# Test joystick hybrid trajectory control
./joystick_hybrid_trajectory_test

# Test teleoperation
./tele_zmq_test

# Run trajectory planning examples
./test/toppra/toppra_example_01

# Test TOPPRA interpolation methods
./examples/toppra_interpolation_methods_demo
```

## API Reference

### Core Classes

- **`industRob`** - Main robot interface class
- **`ZmqComm`** - ZeroMQ communication handler
- **`HybridTrajectoryPlanner`** - Advanced hybrid trajectory planning with online/offline modes
- **`ToppraInterpolator`** - TOPPRA-based trajectory interpolation with multiple methods
- **`RobotModel`** - Kinematics and dynamics model using Pinocchio

### Key Methods

#### Connection & Initialization
- `connect()` - Establish communication
- `disconnect()` - Close communication
- `init()` - Initialize robot system
- `reset()` - Reset robot state

#### Motion Control
- `movAbsJ()` - Absolute joint movement
- `movJ()` - Joint space movement to Cartesian pose
- `movL()` - Linear Cartesian movement
- `servoj()` - Real-time joint servo
- `servoL()` - Real-time Cartesian servo

#### Kinematics
- `robFK()` - Forward kinematics
- `robIk()` - Inverse kinematics

#### Status & Feedback
- `getJoint()` - Get current joint positions
- `getTcpPose()` - Get TCP pose
- `getJointSpeed()` - Get joint velocities
- `getTcpSpeed()` - Get TCP velocity

#### Advanced Trajectory Planning
- `HybridTrajectoryPlanner::addOnlineTarget()` - Add real-time trajectory target
- `HybridTrajectoryPlanner::addTrajectorySegment()` - Add pre-planned trajectory segment
- `ToppraInterpolator::setInterpolationMethod()` - Choose interpolation method
- `ToppraInterpolator::setBoundaryCondition()` - Set boundary conditions

## Communication Protocol

The system uses ZeroMQ for real-time communication with the following endpoints:

### Real-time Data (1000Hz)
- **Motor State**: `/tmp/data_feedback_fifo` (Robot → SDK)
- **Motion Commands**: `/tmp/data_stream_fifo` (SDK → Robot)

### Command Interface (Non-real-time)
- **Commands**: `/tmp/cmd_set_fifo` (SDK → Robot)
- **Responses**: `/tmp/cmd_feedback_fifo` (Robot → SDK)

### Debug Mode
When `ENABLE_DEBUG=ON`, all endpoints are prefixed with `test_`.

## Advanced Features

### Hybrid Trajectory Planning

The system supports seamless switching between online and offline trajectory modes:

- **Online Mode**: Real-time trajectory updates from joystick or external commands
- **Offline Mode**: Pre-planned trajectory execution with smooth transitions
- **Automatic Switching**: Intelligent mode switching based on button states and trajectory completion

### TOPPRA Interpolation Methods

Multiple path interpolation methods to handle different trajectory requirements:

1. **Cubic Spline** (Default)
   - Smooth trajectories with continuous derivatives
   - May slightly overshoot waypoints for smoothness
   - Best for free-space motion

2. **Cubic Hermite Spline** (Recommended for precision)
   - Strictly passes through all waypoints
   - Requires velocity information at waypoints
   - Ideal for precision tasks

3. **Linear Segments**
   - Guaranteed waypoint passage
   - Fast computation
   - Non-smooth at waypoints

### Joystick Control Integration

Real-time joystick control with trajectory planning:

- **Button Mapping**: Configurable button actions for predefined positions
- **Real-time Control**: Smooth joystick-to-robot position mapping
- **Safety Features**: Automatic trajectory conflict resolution
- **Trajectory Recording**: Record and replay joystick movements

## Development

### Building Documentation

```bash
cd build
cmake -DBUILD_DOCUMENTATION=ON ..
make doc
# Open doc/html/index.html in browser
```

### Adding New Tests

1. Create `.cpp` file in `test/` directory
2. CMake will automatically detect and build it
3. Link against `robot_infra` library

### Code Style

- Follow C++17 standards
- Use meaningful variable names
- Document public APIs with Doxygen comments
- Include error handling for all operations

## Troubleshooting

### Common Issues

1. **ZeroMQ Connection Failed**
   ```bash
   # Check if ZeroMQ is properly installed
   pkg-config --modversion libzmq
   ```

2. **Trajectory Planning Library Not Found**
   ```bash
   # Ensure libraries are built and installed
   sudo ldconfig
   ```

3. **Permission Denied on FIFO**
   ```bash
   # Check FIFO permissions
   ls -la /tmp/*fifo
   ```

4. **Joystick Not Detected**
   ```bash
   # Check joystick connection
   python3 -c "import pygame; pygame.init(); print(pygame.joystick.get_count())"
   ```

5. **Trajectory Jumps or Discontinuities**
   ```bash
   # Use Hermite spline interpolation for strict waypoint passage
   interpolator.setInterpolationMethod(PathInterpolationMethod::CUBIC_HERMITE_SPLINE);
   ```

6. **Pinocchio Library Not Found**
   ```bash
   # Check Pinocchio installation
   pkg-config --modversion pinocchio
   ```

### Debug Mode

Enable debug mode to use separate test endpoints:
```bash
cmake -DENABLE_DEBUG=ON ..
```

## Configuration and Best Practices

### Trajectory Planning Configuration

For optimal trajectory planning performance:

```cpp
// Motion constraints (adjust based on your robot)
MotionConstraints<6> constraints;
constraints.max_velocity << 2.0, 1.0, 0.5, 1.0, 1.0, 1.0;  // m/s or rad/s
constraints.max_acceleration.setConstant(8.0);               // m/s² or rad/s²
constraints.max_jerk.setConstant(20.0);                      // m/s³ or rad/s³

// Control frequency (higher = smoother, more CPU intensive)
double control_frequency = 62.5;  // Hz (16ms period)
HybridTrajectoryPlanner<6> planner(1.0 / control_frequency);
```

### Joystick Control Setup

1. **Connect Xbox/PlayStation controller**
2. **Run joystick publisher**:
   ```bash
   cd python
   python3 JoystickController.py
   ```
3. **Start robot control**:
   ```bash
   cd build
   ./joystick_hybrid_trajectory_test
   ```

### TOPPRA Interpolation Method Selection

Choose interpolation method based on requirements:

| Requirement | Recommended Method | Reason |
|-------------|-------------------|---------|
| Strict waypoint passage | `CUBIC_HERMITE_SPLINE` | Guaranteed waypoint passage |
| Maximum smoothness | `CUBIC_SPLINE` with `NOT_A_KNOT` | Smoothest possible trajectory |
| Fast computation | `LINEAR_SEGMENTS` | Simplest calculation |
| Start/stop motion | `CUBIC_SPLINE` with `CLAMPED` | Zero velocity at endpoints |

### Performance Optimization

- **Real-time systems**: Use control frequencies ≤ 100Hz
- **High precision**: Use `CUBIC_HERMITE_SPLINE` with appropriate velocities
- **Smooth motion**: Use `CUBIC_SPLINE` with relaxed waypoint tolerance
- **Memory constrained**: Use `LINEAR_SEGMENTS` for minimal memory usage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

[Specify your license here]

## Contact

[Your contact information]
