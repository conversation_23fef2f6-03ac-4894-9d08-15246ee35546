#include <iostream>
#include <vector>
#include <iomanip>
#include <random>
#include <chrono>
#include <fstream>
#include <Eigen/Dense>
#include "RobotModel.h"

using namespace robot_infra;
using VectorXd = RobotModel::VectorXd;
using MatrixXd = RobotModel::MatrixXd;
using Vector6d = RobotModel::Vector6d;

class IKTestStatistics {
public:
    int total_tests = 0;
    int successful_tests = 0;
    int convergent_tests = 0;  // IK算法收敛的次数
    double total_pose_error = 0.0;
    double total_joint_error = 0.0;
    double max_pose_error = 0.0;
    double max_joint_error = 0.0;
    double min_pose_error = std::numeric_limits<double>::max();
    double min_joint_error = std::numeric_limits<double>::max();

    void addResult(bool ik_success, bool pose_success, double pose_error, double joint_error) {
        total_tests++;
        if (ik_success) convergent_tests++;
        if (pose_success) successful_tests++;

        total_pose_error += pose_error;
        total_joint_error += joint_error;

        max_pose_error = std::max(max_pose_error, pose_error);
        max_joint_error = std::max(max_joint_error, joint_error);
        min_pose_error = std::min(min_pose_error, pose_error);
        min_joint_error = std::min(min_joint_error, joint_error);
    }

    void printStatistics() {
        std::cout << "\n=== 10万次IK测试统计结果 ===" << std::endl;
        std::cout << std::fixed << std::setprecision(4);
        std::cout << "总测试次数: " << total_tests << std::endl;
        std::cout << "IK收敛次数: " << convergent_tests << " (" << (100.0 * convergent_tests / total_tests) << "%)" << std::endl;
        std::cout << "位姿精度成功次数: " << successful_tests << " (" << (100.0 * successful_tests / total_tests) << "%)" << std::endl;

        std::cout << "\n--- 位姿误差统计 ---" << std::endl;
        std::cout << "平均位姿误差: " << (total_pose_error / total_tests) << " m" << std::endl;
        std::cout << "最大位姿误差: " << max_pose_error << " m" << std::endl;
        std::cout << "最小位姿误差: " << min_pose_error << " m" << std::endl;

        std::cout << "\n--- 关节误差统计 ---" << std::endl;
        std::cout << "平均关节误差: " << (total_joint_error / total_tests) << " rad" << std::endl;
        std::cout << "最大关节误差: " << max_joint_error << " rad" << std::endl;
        std::cout << "最小关节误差: " << min_joint_error << " rad" << std::endl;
    }
};

VectorXd generateRandomJointAngles(int joint_count, std::mt19937& gen) {
    VectorXd q(joint_count);
    std::uniform_real_distribution<double> dis(-120.0, 120.0);  // -120到120度

    for (int i = 0; i < joint_count; ++i) {
        q[i] = dis(gen) * M_PI / 180.0;  // 转换为弧度
    }

    return q;
}
const double IK_TOLERANCE = 5e-4;
void massive_ik_test(RobotModel& robot) {
    std::cout << "massive_ik_test" << std::endl;

    // 随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());

    // 测试参数
    const int NUM_TESTS = 100000;
    const double POSE_TOLERANCE = 5e-4;  // 10cm位姿容差
    const int MAX_ITERATIONS = 100;
    //100 : 2.7ms 69%
    //300 : 6ms 76%
    //500 : 10ms 79%
    //1000: 20ms 79%

    //阻尼伪逆 1e-1 平均: 1.532ms/次 成功率: 81%
    //阻尼伪逆 1e-2 平均: 1.42ms/次 成功率: 85%
    //        0.015 平均: 1.378ms/次 成功率: 86.33%
    //        0.016 平均: 1.372ms/次 成功率: 86.43%
    //        0.017 平均: 1.341ms/次 成功率: 87.00%
    //        0.018 平均: 1.369ms/次 成功率: 86.68%
    //        2e-2  平均: 1.349ms/次 成功率: 86.93%
    //        4e-2  平均: 1.370ms/次 成功率: 86.32%
    //阻尼伪逆 5e-2 平均: 1.373ms/次 成功率: 86.1%
    //阻尼伪逆 6e-2 平均: 1.388ms/次 成功率: 85.48%
    //阻尼伪逆 7e-2 平均: 1.41ms/次 成功率: 84.2%
    //阻尼伪逆 1e-3 平均: 1.948ms/次 成功率: 71%
    //自适应阻尼  1.358ms/次 成功率: 85.75%
    

    // 统计对象
    IKTestStatistics stats;

    // 进度显示
    auto start_time = std::chrono::high_resolution_clock::now();

    for (int test_id = 0; test_id < NUM_TESTS; ++test_id) {
        // 生成随机关节角度
        VectorXd q_target = generateRandomJointAngles(robot.getJointCount(), gen);

        // 正运动学得到目标位姿
        Vector6d target_pose = robot.forwardKinematics(q_target);

        // 生成随机初始值
        //VectorXd q_init = generateRandomJointAngles(robot.getJointCount(), gen);
        VectorXd q_init = Eigen::VectorXd::Constant(robot.getJointCount(), 0.5);
        // 逆运动学求解
        VectorXd q_solution(robot.getJointCount());
        bool ik_success = robot.inverseKinematics(q_solution, target_pose, q_init, MAX_ITERATIONS, IK_TOLERANCE);

        // 验证解的精度
        Vector6d achieved_pose = robot.forwardKinematics(q_solution);
        double pose_error = (target_pose - achieved_pose).norm();
        double joint_error = (q_target - q_solution).norm();

        bool pose_success = pose_error < IK_TOLERANCE;

        // 记录统计
        stats.addResult(ik_success, pose_success, pose_error, joint_error);

        // 进度显示（每1000次显示一次）
        if ((test_id + 1) % 1000 == 0) {
            auto current_time = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time);
            double progress = (double)(test_id + 1) / NUM_TESTS * 100.0;

            double avg_time_per_test = (double)elapsed.count() / (test_id + 1);
            std::cout << "进度: " << std::fixed << std::setprecision(1) << progress
                     << "% (" << (test_id + 1) << "/" << NUM_TESTS << ") "
                     << "总用时: " << elapsed.count() << "ms "
                     << "平均: " << std::setprecision(3) << avg_time_per_test << "ms/次 "
                     << "成功率: " << std::setprecision(2) << (100.0 * stats.successful_tests / (test_id + 1)) << "%"
                     << std::endl;
        }
    }

    std::cout << std::endl;

    // 打印最终统计结果
    stats.printStatistics();

    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "\n总用时: " << total_time.count() << " ms" << std::endl;
    std::cout << "平均每次测试用时: " << (double)total_time.count() / NUM_TESTS << " ms" << std::endl;
}

void testJacobian(RobotModel& robot, const VectorXd& q, double epsilon = 1e-6) {
    using namespace Eigen;
    using SE3 = pinocchio::SE3;
    int dof = q.size();

    MatrixXd J_analytical = robot.computeJacobian(q, pinocchio::LOCAL_WORLD_ALIGNED);

    SE3 oMi = robot.getEndEffectorTransform(q);  // 获取末端 SE3

    MatrixXd J_numerical(6, dof);
    for (int i = 0; i < dof; ++i) {
        VectorXd q_plus = q;
        q_plus(i) += epsilon;
        VectorXd q_minus = q;
        q_minus(i) -= epsilon;

        SE3 oM_plus = robot.getEndEffectorTransform(q_plus);
        SE3 oM_minus = robot.getEndEffectorTransform(q_minus);

        SE3 delta = oM_minus.inverse() * oM_plus;
        Vector6d v_local = pinocchio::log6(delta) / (2.0 * epsilon);

        // 将局部速度转换为世界坐标系下的速度
        Vector6d v_world;
        v_world.head<3>() = oMi.rotation() * v_local.head<3>();  // 线速度
        v_world.tail<3>() = oMi.rotation() * v_local.tail<3>();  // 角速度

        J_numerical.col(i) = v_world;
    }

    MatrixXd diff = J_numerical - J_analytical;
    double err = diff.norm();

    std::cout << "[Jacobian Test] Analytical:\n" << J_analytical << std::endl;
    std::cout << "[Jacobian Test] Numerical:\n" << J_numerical << std::endl;
    std::cout << "[Jacobian Test] Error norm = " << err << std::endl;

    if (err < 1e-5) {
        std::cout << "[Jacobian Test] ✅ 雅可比矩阵验证通过！" << std::endl;
    } else {
        std::cout << "[Jacobian Test] ❌ 雅可比矩阵误差过大！" << std::endl;
    }
}


void testIK(RobotModel& robot) {
    std::vector<Eigen::VectorXd> joint_angles;

        // 正确的Eigen向量初始化方式
        Eigen::VectorXd q1(6);
        q1 << 10.0, 20.0, 30.0, 40.0, 50.0, 60.0;
        q1 *= M_PI / 180.0;
        joint_angles.push_back(q1);

        Eigen::VectorXd q2(6);
        q2 << -60.0, 40.0, -20.0, 5.0, -10.0, -30.0;
        q2 *= M_PI / 180.0;
        joint_angles.push_back(q2);

        Eigen::VectorXd q3(6);
        q3 << -10.0, 20.0, -30.0, 40.0, -50.0, 60.0;
        q3 *= M_PI / 180.0;
        joint_angles.push_back(q3);

        Eigen::VectorXd q_init = Eigen::VectorXd::Constant(robot.getJointCount(), 0.5);
        int success_count = 0;
        // 设置输出格式：固定小数点，保留4位小数
        std::cout << std::fixed << std::setprecision(6);

        for (const auto& q : joint_angles) {
            RobotModel::Vector6d pose = robot.forwardKinematics(q);
            std::cout << "------------------------" << std::endl;
            std::cout << "正运动角度: " << q.transpose() << std::endl;
            std::cout << "运动学位姿: " << pose.transpose() << std::endl;

            Eigen::VectorXd q_solution(robot.getJointCount());
            bool ik_success = robot.inverseKinematics(q_solution, pose, q_init, 100, IK_TOLERANCE);
            RobotModel::Vector6d pose_achieved = robot.forwardKinematics(q_solution);

            std::cout << "逆运动学角度: " << q_solution.transpose() << std::endl;
            std::cout << "逆运动学位姿: " << pose_achieved.transpose() << std::endl;
            std::cout << "IK求解状态: " << (ik_success ? "成功" : "失败") << std::endl;

            double pose_error = (pose - pose_achieved).norm();
            double joint_error = (q - q_solution).norm();

            std::cout << "位姿误差: " << pose_error << std::endl;
            std::cout << "关节误差: " << joint_error << std::endl;

            bool success = pose_error < IK_TOLERANCE;  // 50cm tolerance (reasonable for IK)
            std::cout << "结果: " << (success ? "✓ 成功" : "✗ 失败") << std::endl;
            if (success) success_count++;
        }

        std::cout << "\n=== 测试总结 ===" << std::endl;
        std::cout << "成功率: " << success_count << "/" << joint_angles.size()
                  << " (" << std::fixed << std::setprecision(2) << (100.0 * success_count / joint_angles.size()) << "%)" << std::endl;
}

int main() {
    try {
        std::string urdf_path = "config/cartesian_robot/urdf/cartesian_6axis.urdf";
        std::string end_effector_frame = "tool0";
        
        RobotModel robot(urdf_path, end_effector_frame);
        
        if (!robot.initialize()) {
            std::cerr << "Failed to initialize robot model" << std::endl;
            return 1;
        }

        int joint_count = robot.getJointCount();
        std::cout << "机器人关节数量: " << joint_count << std::endl;
        
        // 设置关节限位
        Eigen::VectorXd q_min = Eigen::VectorXd::Constant(joint_count, -M_PI);
        Eigen::VectorXd q_max = Eigen::VectorXd::Constant(joint_count, M_PI);
        Eigen::VectorXd dq_max = Eigen::VectorXd::Constant(joint_count, 2.0);
        Eigen::VectorXd ddq_max = Eigen::VectorXd::Constant(joint_count, 10.0);
        robot.setJointLimits(q_min, q_max, dq_max, ddq_max);
        
        testIK(robot);
        std::cout << "等待getchar() 开始10万次随机IK测试..." << std::endl;
        getchar();
        massive_ik_test(robot);
 
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
