#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <iomanip>
#include <numeric>
#include <algorithm>
#include "RobotModel.h"

using namespace robot_infra;
using namespace Eigen;

class PerformanceBenchmark {
private:
    RobotModel robot_model_;
    std::mt19937 gen_;
    std::uniform_real_distribution<double> pos_dist_;
    std::uniform_real_distribution<double> ori_dist_;
    
public:
    PerformanceBenchmark()
        : robot_model_("config/cartesian_robot/urdf/cartesian_6axis.urdf", "end_effector")
        , gen_(std::random_device{}())
        , pos_dist_(-0.5, 0.5)
        , ori_dist_(-M_PI/4, M_PI/4) {

        if (!robot_model_.initialize()) {
            throw std::runtime_error("Failed to initialize robot model");
        }
    }
    
    // 生成随机目标位姿
    RobotModel::Vector6d generateRandomPose() {
        RobotModel::Vector6d pose;
        pose << pos_dist_(gen_), pos_dist_(gen_), std::abs(pos_dist_(gen_)) + 0.05,  // 确保Z>0
                ori_dist_(gen_), ori_dist_(gen_), ori_dist_(gen_);
        return pose;
    }
    
    // 生成随机初始关节角
    RobotModel::VectorXd generateRandomJoints() {
        RobotModel::VectorXd q(6);
        for (int i = 0; i < 6; ++i) {
            q(i) = ori_dist_(gen_);
        }
        return q;
    }
    
    // 单次IK性能测试
    struct IKResult {
        bool success;
        double time_ms;
        int iterations;
        double final_error;
    };
    
    IKResult testSingleIK(const RobotModel::Vector6d& target_pose, 
                         const RobotModel::VectorXd& q_init,
                         int max_iter = 100,
                         double tol = 1e-6) {
        
        auto start = std::chrono::high_resolution_clock::now();
        
        RobotModel::VectorXd q_result;
        bool success = robot_model_.inverseKinematics(q_result, target_pose, q_init, max_iter, tol);
        
        auto end = std::chrono::high_resolution_clock::now();
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        
        // 计算最终误差
        auto final_pose_vec = robot_model_.forwardKinematics(q_result);
        RobotModel::Vector6d error;
        error.head<3>() = target_pose.head<3>() - final_pose_vec.head<3>();

        // 旋转误差计算（简化版本）
        error.tail<3>() = target_pose.tail<3>() - final_pose_vec.tail<3>();
        
        return {success, time_ms, 0, error.norm()};  // iterations暂时设为0
    }
    
    // 批量性能测试
    void runBenchmark(int num_tests = 1000) {
        std::cout << "=== IK性能基准测试 ===" << std::endl;
        std::cout << "测试数量: " << num_tests << std::endl;
        std::cout << "优化版本: 启用快速模式和性能优化" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
        
        std::vector<double> times;
        std::vector<double> errors;
        int success_count = 0;
        int total_iterations = 0;
        
        auto benchmark_start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < num_tests; ++i) {
            // 生成随机测试用例
            auto target_pose = generateRandomPose();
            auto q_init = generateRandomJoints();
            
            // 执行IK测试
            auto result = testSingleIK(target_pose, q_init);
            
            times.push_back(result.time_ms);
            errors.push_back(result.final_error);
            
            if (result.success) {
                success_count++;
            }
            
            // 进度显示
            if ((i + 1) % 100 == 0) {
                double avg_time = std::accumulate(times.end() - 100, times.end(), 0.0) / 100.0;
                double success_rate = (double)success_count / (i + 1) * 100.0;
                std::cout << "进度: " << std::fixed << std::setprecision(1) 
                          << (double)(i + 1) / num_tests * 100.0 << "% (" 
                          << i + 1 << "/" << num_tests << ") "
                          << "最近100次平均: " << std::setprecision(3) << avg_time << "ms "
                          << "成功率: " << std::setprecision(2) << success_rate << "%" << std::endl;
            }
        }
        
        auto benchmark_end = std::chrono::high_resolution_clock::now();
        double total_time = std::chrono::duration<double, std::milli>(benchmark_end - benchmark_start).count();
        
        // 统计分析
        std::sort(times.begin(), times.end());
        std::sort(errors.begin(), errors.end());
        
        double avg_time = std::accumulate(times.begin(), times.end(), 0.0) / times.size();
        double median_time = times[times.size() / 2];
        double p95_time = times[static_cast<size_t>(times.size() * 0.95)];
        double p99_time = times[static_cast<size_t>(times.size() * 0.99)];
        
        double success_rate = (double)success_count / num_tests * 100.0;
        double avg_error = std::accumulate(errors.begin(), errors.end(), 0.0) / errors.size();
        
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "=== 性能统计结果 ===" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        std::cout << "总体性能:" << std::endl;
        std::cout << "  总测试时间: " << std::fixed << std::setprecision(2) << total_time << " ms" << std::endl;
        std::cout << "  成功率: " << std::setprecision(2) << success_rate << "%" << std::endl;
        std::cout << "  成功次数: " << success_count << "/" << num_tests << std::endl;
        
        std::cout << "\n单次IK计算时间统计:" << std::endl;
        std::cout << "  平均时间: " << std::setprecision(3) << avg_time << " ms" << std::endl;
        std::cout << "  中位数时间: " << std::setprecision(3) << median_time << " ms" << std::endl;
        std::cout << "  95%分位数: " << std::setprecision(3) << p95_time << " ms" << std::endl;
        std::cout << "  99%分位数: " << std::setprecision(3) << p99_time << " ms" << std::endl;
        std::cout << "  最快时间: " << std::setprecision(3) << times.front() << " ms" << std::endl;
        std::cout << "  最慢时间: " << std::setprecision(3) << times.back() << " ms" << std::endl;
        
        std::cout << "\n精度统计:" << std::endl;
        std::cout << "  平均误差: " << std::scientific << std::setprecision(2) << avg_error << std::endl;
        std::cout << "  中位数误差: " << std::scientific << std::setprecision(2) << errors[errors.size()/2] << std::endl;
        
        std::cout << "\n性能等级评估:" << std::endl;
        if (avg_time < 5.0) {
            std::cout << "  🚀 优秀 (< 5ms)" << std::endl;
        } else if (avg_time < 10.0) {
            std::cout << "  ✅ 良好 (5-10ms)" << std::endl;
        } else if (avg_time < 15.0) {
            std::cout << "  ⚠️  一般 (10-15ms)" << std::endl;
        } else {
            std::cout << "  ❌ 需要优化 (> 15ms)" << std::endl;
        }
        
        std::cout << "\n优化建议:" << std::endl;
        if (success_rate < 80.0) {
            std::cout << "  - 成功率偏低，建议调整收敛参数或增加最大迭代次数" << std::endl;
        }
        if (p95_time > avg_time * 2) {
            std::cout << "  - 存在性能异常值，建议检查特殊情况处理" << std::endl;
        }
        if (avg_time > 10.0) {
            std::cout << "  - 平均计算时间较长，建议进一步优化算法" << std::endl;
        }
        
        std::cout << std::string(60, '=') << std::endl;
    }
};

int main() {
    try {
        PerformanceBenchmark benchmark;
        benchmark.runBenchmark(1000);  // 测试1000次
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
